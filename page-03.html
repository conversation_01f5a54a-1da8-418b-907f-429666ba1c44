<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3. 优化人才发展与留存 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .main-container {
            height: calc(100vh - 60px);
            display: flex;
            gap: 1rem;
        }
        .left-panel {
            width: 35%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .right-panel {
            width: 65%;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            overflow-y: auto;
        }
        .metrics-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .insights-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            flex: 1;
        }
        .status-normal {
            color: #374151;
            font-weight: 600;
        }
        .status-warning {
            color: #6b7280;
            font-weight: 600;
        }
        .status-danger {
            color: #1f2937;
            font-weight: 600;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .chart-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }
        .chart-container {
            height: 200px;
            position: relative;
        }
        .chart-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-left: 4px solid #374151;
            padding-left: 0.75rem;
        }
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">优化人才发展与留存</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="main-container">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Metrics Table -->
                <div class="metrics-table">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900">优化人才发展与留存 - 指标监控</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-xs">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">指标名称</th>
                                    <th class="px-2 py-2 text-center font-medium text-gray-700">状态</th>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">异常说明</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">绩优代理人流失率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">核心生产力流失严重</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">关键岗替补深度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">继任者储备不足</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">技能升级速度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">高潜人才流失率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">后备干部断层风险</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">跨岗流动率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">培训产能转化率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">培训效果转化不佳</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">晋升周期压缩比</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">离职面谈预警指数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">系统性管理问题突出</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3">整体洞见分析</h4>
                    <div class="text-xs text-gray-700 space-y-2">
                        <div class="p-2 bg-gray-100 border-l-4 border-gray-800 rounded">
                            <p class="font-medium text-gray-900">关键风险</p>
                            <p class="text-gray-700">绩优代理人流失率偏高，离职面谈反映系统性管理问题，影响团队稳定性。</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-600 rounded">
                            <p class="font-medium text-gray-800">改进建议</p>
                            <p class="text-gray-700">1. 加强关键岗位继任者培养，建立人才梯队<br>
                            2. 优化培训体系，提升培训效果转化率<br>
                            3. 深入分析离职原因，改善管理机制</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-400 rounded">
                            <p class="font-medium text-gray-700">积极表现</p>
                            <p class="text-gray-600">技能升级速度、跨岗流动率等指标表现良好，员工发展通道相对顺畅。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Charts -->
            <div class="right-panel">
                <div class="section-title">数据可视化分析</div>

                <div class="chart-grid">
                    <div class="chart-item">
                        <div class="chart-title">绩优代理人流失率趋势</div>
                        <div class="chart-container">
                            <canvas id="eliteAgentTurnoverChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">关键岗位继任者储备</div>
                        <div class="chart-container">
                            <canvas id="successorDepthChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">技能升级进度分析</div>
                        <div class="chart-container">
                            <canvas id="skillUpgradeChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">培训产能转化效果</div>
                        <div class="chart-container">
                            <canvas id="trainingConversionChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-item">
                    <div class="chart-title">离职原因分析</div>
                    <div class="chart-container">
                        <canvas id="resignationAnalysisChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
    const colors = {
        primary: '#1f2937',
        secondary: '#374151',
        tertiary: '#6b7280',
        light: '#9ca3af',
        lighter: '#d1d5db',
        lightest: '#f3f4f6'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    font: { size: 10 }
                }
            },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: { color: colors.lightest },
                ticks: { font: { size: 10 } }
            },
            x: {
                grid: { display: false },
                ticks: { font: { size: 10 } }
            }
        }
    };

    // 绩优代理人流失率趋势
    new Chart(document.getElementById('eliteAgentTurnoverChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            datasets: [{
                label: '流失率',
                data: [8, 12, 15, 18],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                fill: true,
                tension: 0.4
            }, {
                label: '目标线',
                data: [10, 10, 10, 10],
                borderColor: colors.secondary,
                borderDash: [5, 5],
                fill: false
            }]
        },
        options: chartDefaults
    });

    // 关键岗位继任者储备
    new Chart(document.getElementById('successorDepthChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['精算总控', '投资总监', '风控总监', '销售总监'],
            datasets: [{
                label: '继任者人数',
                data: [1, 0, 2, 3],
                backgroundColor: [colors.tertiary, colors.primary, colors.secondary, colors.light]
            }]
        },
        options: chartDefaults
    });

    // 技能升级进度分析
    new Chart(document.getElementById('skillUpgradeChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['已完成认证', '进行中', '未开始'],
            datasets: [{
                data: [65, 25, 10],
                backgroundColor: [colors.primary, colors.secondary, colors.lightest],
                borderWidth: 0
            }]
        },
        options: {
            ...chartDefaults,
            plugins: {
                ...chartDefaults.plugins,
                legend: { display: true, position: 'bottom' }
            }
        }
    });

    // 培训产能转化效果
    new Chart(document.getElementById('trainingConversionChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['销售技能', '产品知识', '合规培训', '管理技能'],
            datasets: [{
                label: '转化率',
                data: [68, 75, 85, 58],
                backgroundColor: [colors.light, colors.tertiary, colors.secondary, colors.primary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                ...chartDefaults.scales,
                y: { ...chartDefaults.scales.y, max: 100 }
            }
        }
    });

    // 离职原因分析
    new Chart(document.getElementById('resignationAnalysisChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['薪酬待遇', '发展机会', '管理问题', '工作压力', '其他'],
            datasets: [{
                label: '权重得分',
                data: [35, 28, 22, 18, 12],
                backgroundColor: colors.secondary
            }]
        },
        options: chartDefaults
    });
    </script>
</body>
</html>