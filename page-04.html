<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4. 驱动人力资本效能 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .main-container {
            height: calc(100vh - 60px);
            display: flex;
            gap: 1rem;
        }
        .left-panel {
            width: 35%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .right-panel {
            width: 65%;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            overflow-y: auto;
        }
        .metrics-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .insights-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            flex: 1;
        }
        .status-normal {
            color: #374151;
            font-weight: 600;
        }
        .status-warning {
            color: #6b7280;
            font-weight: 600;
        }
        .status-danger {
            color: #1f2937;
            font-weight: 600;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .chart-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }
        .chart-container {
            height: 200px;
            position: relative;
        }
        .chart-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-left: 4px solid #374151;
            padding-left: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">驱动人力资本效能</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="main-container">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Metrics Table -->
                <div class="metrics-table">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900">驱动人力资本效能 - 指标监控</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-xs">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">指标名称</th>
                                    <th class="px-2 py-2 text-center font-medium text-gray-700">状态</th>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">异常说明</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">销售人均产能</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">理赔时效指数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">处理时长超标，影响客户满意度</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">核保质量系数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">客服价值损耗率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">重复来电率高，服务效率待提升</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">科技需求响应速度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">响应周期偏长，影响业务支撑</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">人力成本收益率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">有效活动率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">销售队伍活动效率有待提升</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">培训成本效能</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3">整体洞见分析</h4>
                    <div class="text-xs text-gray-700 space-y-2">
                        <div class="p-2 bg-gray-100 border-l-4 border-gray-800 rounded">
                            <p class="font-medium text-gray-900">关键风险</p>
                            <p class="text-gray-700">客服价值损耗率异常，重复来电率高，影响客户体验和运营效率。</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-600 rounded">
                            <p class="font-medium text-gray-800">改进建议</p>
                            <p class="text-gray-700">1. 优化理赔流程，缩短处理时长<br>
                            2. 提升科技需求响应速度，加强业务支撑<br>
                            3. 改善销售队伍活动管理，提高有效活动率</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-400 rounded">
                            <p class="font-medium text-gray-700">积极表现</p>
                            <p class="text-gray-600">销售人均产能、核保质量系数等核心效能指标表现稳定，人力资本ROI良好。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Charts -->
            <div class="right-panel">
                <div class="section-title">数据可视化分析</div>

                <div class="chart-grid">
                    <div class="chart-item">
                        <div class="chart-title">销售人均产能趋势</div>
                        <div class="chart-container">
                            <canvas id="salesProductivityChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">理赔时效指数分析</div>
                        <div class="chart-container">
                            <canvas id="claimEfficiencyChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">客服价值损耗率</div>
                        <div class="chart-container">
                            <canvas id="serviceValueLossChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">人力成本收益率</div>
                        <div class="chart-container">
                            <canvas id="hrROIChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-item">
                    <div class="chart-title">培训成本效能对比</div>
                    <div class="chart-container">
                        <canvas id="trainingEfficiencyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
    const colors = {
        primary: '#1f2937',
        secondary: '#374151',
        tertiary: '#6b7280',
        light: '#9ca3af',
        lighter: '#d1d5db',
        lightest: '#f3f4f6'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    font: { size: 10 }
                }
            },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: { color: colors.lightest },
                ticks: { font: { size: 10 } }
            },
            x: {
                grid: { display: false },
                ticks: { font: { size: 10 } }
            }
        }
    };

    // 销售人均产能趋势
    new Chart(document.getElementById('salesProductivityChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '个险渠道',
                data: [45, 48, 52, 49, 55, 58],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                fill: true,
                tension: 0.4
            }, {
                label: '银保渠道',
                data: [32, 35, 38, 36, 41, 43],
                borderColor: colors.secondary,
                backgroundColor: colors.secondary + '20',
                fill: true,
                tension: 0.4
            }]
        },
        options: chartDefaults
    });

    // 理赔时效指数分析
    new Chart(document.getElementById('claimEfficiencyChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['简单案件', '复杂案件', '重大案件'],
            datasets: [{
                label: '实际时效指数',
                data: [0.8, 1.2, 1.5],
                backgroundColor: colors.secondary
            }, {
                label: '目标时效指数',
                data: [1.0, 1.0, 1.0],
                backgroundColor: colors.tertiary
            }]
        },
        options: chartDefaults
    });

    // 客服价值损耗率
    new Chart(document.getElementById('serviceValueLossChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['重复来电', '一次解决'],
            datasets: [{
                data: [25, 75],
                backgroundColor: [colors.primary, colors.lightest],
                borderWidth: 0
            }]
        },
        options: {
            ...chartDefaults,
            plugins: {
                ...chartDefaults.plugins,
                legend: { display: true, position: 'bottom' }
            }
        }
    });

    // 人力成本收益率
    new Chart(document.getElementById('hrROIChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            datasets: [{
                label: 'ROI (%)',
                data: [125, 132, 128, 135],
                backgroundColor: [colors.light, colors.tertiary, colors.secondary, colors.primary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                ...chartDefaults.scales,
                y: { ...chartDefaults.scales.y, min: 100, max: 150 }
            }
        }
    });

    // 培训成本效能对比
    new Chart(document.getElementById('trainingEfficiencyChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['销售培训', '管理培训', '技能培训', '合规培训'],
            datasets: [{
                label: '投入成本(万元)',
                data: [120, 80, 150, 60],
                backgroundColor: colors.secondary
            }, {
                label: '产出效益(万元)',
                data: [180, 95, 210, 75],
                backgroundColor: colors.tertiary
            }]
        },
        options: chartDefaults
    });
    </script>
</body>
</html>
