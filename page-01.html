<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1. 支撑业务战略落地 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .metric-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        .metric-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .section-header {
            border-left: 4px solid #374151;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
        }
        .chart-container {
            height: 120px;
            position: relative;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }
        .target-text {
            font-size: 0.75rem;
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">支撑业务战略落地</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 战略人力规划 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">战略人力规划</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">96%</div>
                        <div class="metric-label">战略人力规划准确率</div>
                        <div class="target-text">目标: 95%</div>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="strategicPlanningAccuracyChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 96%"></div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">85%</div>
                        <div class="metric-label">关键岗位招聘完成率</div>
                        <div class="target-text">目标: 90%</div>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="keyPositionRecruitmentRateChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-600 h-2 rounded-full" style="width: 85%"></div>
                </div>
            </div>
        </div>

        <!-- 组织诊断 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">组织诊断</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="metric-label mb-2">季度组织诊断与改进建议</div>
                <div class="target-text mb-4">目标: 每季度1次, 至少3个建议</div>
                <div class="chart-container">
                    <canvas id="orgDiagnosisChart"></canvas>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">80</div>
                        <div class="metric-label">员工敬业度"组织支持"得分</div>
                        <div class="target-text">目标: 提升10% (82.5)</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">↑ 6.7%</div>
                        <div class="text-xs text-gray-500">vs 上次</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="employeeEngagementChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 预算管理 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">预算管理</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">3%</div>
                        <div class="metric-label">人力成本预算执行偏差</div>
                        <div class="target-text">目标: ±5% 以内</div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <span class="text-green-600 text-xs font-semibold">✓</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="budgetDeviationChart"></canvas>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="metric-label mb-2">人力成本 vs 营收增长率</div>
                <div class="target-text mb-4">目标: 人力成本增长率 ≤ 营收增长率</div>
                <div class="chart-container">
                    <canvas id="costRevenueGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
    const grayColors = {
        primary: '#374151',
        secondary: '#6b7280',
        light: '#d1d5db',
        lighter: '#f3f4f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        }
    };

    // Small doughnut charts
    new Chart(document.getElementById('strategicPlanningAccuracyChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [96, 4],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    new Chart(document.getElementById('keyPositionRecruitmentRateChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [85, 15],
                backgroundColor: [grayColors.secondary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Org Diagnosis Chart
    new Chart(document.getElementById('orgDiagnosisChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['Q1', 'Q2'],
            datasets: [
                { label: '诊断次数', data: [1, 1], backgroundColor: grayColors.primary },
                { label: '改进建议数', data: [4, 3], backgroundColor: grayColors.secondary }
            ]
        },
        options: {
            ...chartDefaults,
            plugins: { ...chartDefaults.plugins, legend: { display: true, position: 'bottom' } },
            scales: {
                y: { beginAtZero: true, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Employee Engagement Chart
    new Chart(document.getElementById('employeeEngagementChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['上次', '当前', '目标'],
            datasets: [{
                data: [75, 80, 82.5],
                backgroundColor: [grayColors.light, grayColors.primary, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 100, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Budget Deviation Chart
    new Chart(document.getElementById('budgetDeviationChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前偏差'],
            datasets: [{
                data: [3],
                backgroundColor: grayColors.primary
            }]
        },
        options: {
            ...chartDefaults,
            indexAxis: 'y',
            scales: {
                x: { beginAtZero: true, max: 6, grid: { color: grayColors.lighter } },
                y: { grid: { display: false } }
            }
        }
    });

    // Cost vs Revenue Growth Chart
    new Chart(document.getElementById('costRevenueGrowthChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['人力成本增长率', '营收增长率'],
            datasets: [{
                data: [8, 12],
                backgroundColor: [grayColors.secondary, grayColors.primary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });
    </script>
</body>
</html>