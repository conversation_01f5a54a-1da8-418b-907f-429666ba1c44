<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2. 保障人才持续输入 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .main-container {
            height: calc(100vh - 60px);
            display: flex;
            gap: 1rem;
        }
        .left-panel {
            width: 35%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .right-panel {
            width: 65%;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            overflow-y: auto;
        }
        .metrics-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .insights-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            flex: 1;
        }
        .status-normal {
            color: #10b981;
            font-weight: 600;
        }
        .status-warning {
            color: #f59e0b;
            font-weight: 600;
        }
        .status-danger {
            color: #ef4444;
            font-weight: 600;
        }
        .tooltip {
            position: relative;
            cursor: help;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .tooltip-text {
            visibility: hidden;
            opacity: 0;
            width: 200px;
            background-color: #374151;
            color: white;
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            font-size: 12px;
            line-height: 1.4;
            transition: opacity 0.3s;
        }
        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #374151 transparent transparent transparent;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .chart-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }
        .chart-container {
            height: 200px;
            position: relative;
        }
        .chart-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-left: 4px solid #374151;
            padding-left: 0.75rem;
        }
        .quick-nav {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 0.5rem;
            z-index: 1000;
        }
        .nav-item {
            display: block;
            width: 40px;
            height: 40px;
            margin: 0.25rem 0;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        .nav-item:hover {
            background: #374151;
            color: white;
            transform: scale(1.1);
        }
        .nav-item.active {
            background: #374151;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">保障人才持续输入</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="quick-nav">
        <a href="page-01.html" class="nav-item" title="支撑业务战略落地">1</a>
        <a href="page-02.html" class="nav-item active" title="保障人才持续输入">2</a>
        <a href="page-03.html" class="nav-item" title="优化人才发展与留存">3</a>
        <a href="page-04.html" class="nav-item" title="驱动人力资本效能">4</a>
        <a href="page-05.html" class="nav-item" title="规避人才相关风险">5</a>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="main-container">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Metrics Table -->
                <div class="metrics-table">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900">保障人才持续输入 - 指标监控</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-xs">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">指标名称</th>
                                    <th class="px-2 py-2 text-center font-medium text-gray-700">状态</th>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">异常说明</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">销售增员转化率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">转化率偏低，招募效率待提升</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">核心岗供给缺口</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">精算、核保等稀缺岗位缺口较大</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">科技岗招聘周期</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">招聘周期过长，影响项目进度</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">校招留存率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">内部人才池浓度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">渠道效能指数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">部分渠道效能偏低，需优化投入</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">岗位薪酬竞争力</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">科技、投资岗位薪酬竞争力不足</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">新人上岗速度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3">整体洞见分析</h4>
                    <div class="text-xs text-gray-700 space-y-2">
                        <div class="p-2 bg-gray-100 border-l-4 border-gray-800 rounded">
                            <p class="font-medium text-gray-900">关键风险</p>
                            <p class="text-gray-700">核心岗位供给缺口较大，薪酬竞争力不足，影响关键人才引进。</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-600 rounded">
                            <p class="font-medium text-gray-800">改进建议</p>
                            <p class="text-gray-700">1. 提升科技、投资等关键岗位薪酬竞争力<br>
                            2. 优化招聘渠道配置，缩短招聘周期<br>
                            3. 加强销售增员培训，提升转化率</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-400 rounded">
                            <p class="font-medium text-gray-700">积极表现</p>
                            <p class="text-gray-600">校招留存率、内部人才池浓度等指标表现稳定，为人才梯队建设奠定基础。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Charts -->
            <div class="right-panel">
                <div class="section-title">数据可视化分析</div>

                <div class="chart-grid">
                    <div class="chart-item">
                        <div class="chart-title">销售增员转化率趋势</div>
                        <div class="chart-container">
                            <canvas id="salesConversionChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">核心岗位供给缺口分析</div>
                        <div class="chart-container">
                            <canvas id="corePositionGapChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">科技岗招聘周期对比</div>
                        <div class="chart-container">
                            <canvas id="techRecruitmentChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">渠道效能指数分析</div>
                        <div class="chart-container">
                            <canvas id="channelEfficiencyChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-item">
                    <div class="chart-title">岗位薪酬竞争力对比</div>
                    <div class="chart-container">
                        <canvas id="salaryCompetitivenessChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
                <div class="chart-container mb-4">
                    <canvas id="offerAcceptanceRateChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 88%"></div>
                </div>
            </div>
        </div>

        <!-- 招聘流程优化 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">招聘流程优化</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">35</div>
                        <div class="metric-label">平均招聘周期（天）</div>
                        <div class="target-text">目标: 30天</div>
                    </div>
                    <div class="status-badge status-warning">需改进</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="recruitmentCycleChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">较目标超出 5 天，需要优化流程效率</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">92%</div>
                        <div class="metric-label">候选人面试体验满意度</div>
                        <div class="target-text">目标: 90%</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="candidateExperienceChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 92%"></div>
                </div>
            </div>
        </div>

        <!-- 雇主品牌建设 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">雇主品牌建设</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="metric-label mb-2">主流招聘平台排名</div>
                <div class="target-text mb-4">目标: 前三</div>
                <div class="chart-container mb-4">
                    <canvas id="platformRankingChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">平台A: 第2名 ✓ | 平台B: 第4名 | 平台C: 第3名 ✓</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">150%</div>
                        <div class="metric-label">公众号粉丝年度增长</div>
                        <div class="target-text">目标: 50%</div>
                    </div>
                    <div class="status-badge status-good">✓ 超额</div>
                </div>
                <div class="chart-container">
                    <canvas id="followerGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
    const colors = {
        primary: '#1f2937',
        secondary: '#374151',
        tertiary: '#6b7280',
        light: '#9ca3af',
        lighter: '#d1d5db',
        lightest: '#f3f4f6'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    font: { size: 10 }
                }
            },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: { color: colors.lightest },
                ticks: { font: { size: 10 } }
            },
            x: {
                grid: { display: false },
                ticks: { font: { size: 10 } }
            }
        }
    };

    // 销售增员转化率趋势
    new Chart(document.getElementById('salesConversionChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '转化率',
                data: [15, 18, 16, 14, 17, 19],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                fill: true,
                tension: 0.4
            }, {
                label: '目标线',
                data: [20, 20, 20, 20, 20, 20],
                borderColor: colors.secondary,
                borderDash: [5, 5],
                fill: false
            }]
        },
        options: chartDefaults
    });

    // 核心岗位供给缺口分析
    new Chart(document.getElementById('corePositionGapChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['精算师', '核保师', '投资经理', '风控专员'],
            datasets: [{
                label: '缺口人数',
                data: [12, 8, 15, 6],
                backgroundColor: [colors.primary, colors.secondary, colors.primary, colors.tertiary]
            }]
        },
        options: chartDefaults
    });

    // 科技岗招聘周期对比
    new Chart(document.getElementById('techRecruitmentChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['算法工程师', '前端开发', '后端开发', '数据分析师'],
            datasets: [{
                label: '实际周期(天)',
                data: [45, 35, 38, 28],
                backgroundColor: colors.secondary
            }, {
                label: '目标周期(天)',
                data: [35, 30, 30, 25],
                backgroundColor: colors.tertiary
            }]
        },
        options: chartDefaults
    });

    // 渠道效能指数分析
    new Chart(document.getElementById('channelEfficiencyChart').getContext('2d'), {
        type: 'radar',
        data: {
            labels: ['猎头', '内推', '校招', '社招', '实习转正'],
            datasets: [{
                label: '录用率',
                data: [75, 85, 60, 45, 80],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '30',
                pointBackgroundColor: colors.primary
            }, {
                label: '成本效率',
                data: [60, 90, 85, 70, 95],
                borderColor: colors.secondary,
                backgroundColor: colors.secondary + '20',
                pointBackgroundColor: colors.secondary
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: { font: { size: 8 } }
                }
            }
        }
    });

    // 岗位薪酬竞争力对比
    new Chart(document.getElementById('salaryCompetitivenessChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['科技岗', '投资岗', '精算岗', '销售岗', '运营岗'],
            datasets: [{
                label: '公司薪酬分位值',
                data: [65, 70, 80, 85, 75],
                backgroundColor: colors.secondary
            }, {
                label: '市场75分位值',
                data: [85, 90, 85, 80, 75],
                backgroundColor: colors.tertiary
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                ...chartDefaults.scales,
                y: { ...chartDefaults.scales.y, max: 100 }
            }
        }
    });
    </script>
</body>
</html>
