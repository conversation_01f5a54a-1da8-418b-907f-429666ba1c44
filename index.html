<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .hero-section {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            height: 25vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .main-container {
            height: 75vh;
            position: relative;
            overflow: hidden;
        }
        .process-node {
            position: absolute;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            width: 280px;
            min-height: 120px;
        }
        .process-node:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-4px);
            border-color: #374151;
        }
        .node-number {
            position: absolute;
            top: -12px;
            left: 20px;
            background: #374151;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.75rem;
        }
        .node-title {
            font-size: 1rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
            margin-top: 0.5rem;
        }
        .node-desc {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.4;
        }
        .connection-line {
            position: absolute;
            background: #d1d5db;
            z-index: 1;
        }
        .connection-arrow {
            position: absolute;
            width: 0;
            height: 0;
            z-index: 2;
        }
        /* 节点位置 */
        .node-1 { top: 50px; left: 50px; }
        .node-2 { top: 50px; right: 50px; }
        .node-3 { top: 200px; left: 50%; transform: translateX(-50%); }
        .node-4 { bottom: 100px; left: 50px; }
        .node-5 { bottom: 100px; right: 50px; }

        /* 连接线 */
        .line-1-3 {
            top: 170px;
            left: 190px;
            width: 2px;
            height: 60px;
        }
        .line-2-3 {
            top: 170px;
            right: 190px;
            width: 2px;
            height: 60px;
        }
        .line-3-4 {
            bottom: 220px;
            left: 50%;
            width: 2px;
            height: 60px;
            transform: translateX(-50%) rotate(-45deg);
            transform-origin: top;
        }
        .line-3-5 {
            bottom: 220px;
            right: 50%;
            width: 2px;
            height: 60px;
            transform: translateX(50%) rotate(45deg);
            transform-origin: top;
        }

        /* 箭头 */
        .arrow-down {
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #6b7280;
        }
        .arrow-1-3 { top: 224px; left: 188px; }
        .arrow-2-3 { top: 224px; right: 188px; }
        .arrow-3-4 { bottom: 166px; left: calc(50% - 20px); }
        .arrow-3-5 { bottom: 166px; right: calc(50% - 20px); }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="text-center">
            <h1 class="text-3xl font-bold mb-2">人力资源战略仪表盘</h1>
            <p class="text-lg text-gray-300">
                全面监控人力资源关键指标，支撑业务战略决策，驱动组织效能提升
            </p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        <!-- 连接线 -->
        <div class="connection-line line-1-3"></div>
        <div class="connection-line line-2-3"></div>
        <div class="connection-line line-3-4"></div>
        <div class="connection-line line-3-5"></div>

        <!-- 箭头 -->
        <div class="connection-arrow arrow-down arrow-1-3"></div>
        <div class="connection-arrow arrow-down arrow-2-3"></div>
        <div class="connection-arrow arrow-down arrow-3-4"></div>
        <div class="connection-arrow arrow-down arrow-3-5"></div>

        <!-- 节点1: 支撑业务战略落地 -->
        <div class="process-node node-1" onclick="window.location.href='page-01.html'">
            <div class="node-number">1</div>
            <div class="node-title">支撑业务战略落地</div>
            <div class="node-desc">
                确保人力资源规划与业务战略高度协同<br>
                • 战略岗位满足率 96%<br>
                • 数字化人才占比提升<br>
                • 编制执行偏差度控制
            </div>
        </div>

        <!-- 节点2: 保障人才持续输入 -->
        <div class="process-node node-2" onclick="window.location.href='page-02.html'">
            <div class="node-number">2</div>
            <div class="node-title">保障人才持续输入</div>
            <div class="node-desc">
                构建高效招聘体系，确保人才供给质量<br>
                • 销售增员转化率优化<br>
                • 核心岗位供给缺口管控<br>
                • 科技岗招聘周期缩短
            </div>
        </div>

        <!-- 节点3: 优化人才发展与留存 -->
        <div class="process-node node-3" onclick="window.location.href='page-03.html'">
            <div class="node-number">3</div>
            <div class="node-title">优化人才发展与留存</div>
            <div class="node-desc">
                提升员工能力与满意度，降低核心人才流失<br>
                • 绩优代理人流失率控制<br>
                • 关键岗位继任者储备<br>
                • 培训产能转化率提升
            </div>
        </div>

        <!-- 节点4: 驱动人力资本效能 -->
        <div class="process-node node-4" onclick="window.location.href='page-04.html'">
            <div class="node-number">4</div>
            <div class="node-title">驱动人力资本效能</div>
            <div class="node-desc">
                优化人力成本结构，提升组织运营效率<br>
                • 销售人均产能提升<br>
                • 理赔时效指数优化<br>
                • 人力成本收益率管控
            </div>
        </div>

        <!-- 节点5: 规避人才相关风险 -->
        <div class="process-node node-5" onclick="window.location.href='page-05.html'">
            <div class="node-number">5</div>
            <div class="node-title">规避人才相关风险</div>
            <div class="node-desc">
                建立风险预警机制，保障人力资源合规运营<br>
                • 关键岗位空缺风险预警<br>
                • 员工违规事件监控<br>
                • 业务连续性风险评估
            </div>
        </div>
    </div>



</body>
</html>
