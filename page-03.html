<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3. 优化人才发展与留存 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .metric-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        .metric-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .section-header {
            border-left: 4px solid #374151;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
        }
        .chart-container {
            height: 140px;
            position: relative;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }
        .target-text {
            font-size: 0.75rem;
            color: #9ca3af;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-good { background: #dcfce7; color: #166534; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-danger { background: #fecaca; color: #991b1b; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">优化人才发展与留存</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 培训与发展 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">培训与发展</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">96%</div>
                        <div class="metric-label">关键岗位培训参与率</div>
                        <div class="target-text">目标: 95%</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="trainingParticipationChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 96%"></div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">82%</div>
                        <div class="metric-label">培训项目满意度</div>
                        <div class="target-text">目标: 85%</div>
                    </div>
                    <div class="status-badge status-warning">接近达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="trainingSatisfactionChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-600 h-2 rounded-full" style="width: 82%"></div>
                </div>
            </div>
        </div>

        <!-- 绩效管理 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">绩效管理</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="metric-label mb-2">绩效分布</div>
                <div class="target-text mb-4">目标: A/B+ 占比 20-30%</div>
                <div class="chart-container mb-4">
                    <canvas id="performanceDistributionChart"></canvas>
                </div>
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-500">当前 A/B+ 占比: 25%</div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">85%</div>
                        <div class="metric-label">低绩效员工改进计划完成率</div>
                        <div class="target-text">目标: 80%</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="improvementPlanChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 85%"></div>
                </div>
            </div>
        </div>

        <!-- 员工关系与保留 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">员工关系与保留</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">4%</div>
                        <div class="metric-label">核心人才主动离职率</div>
                        <div class="target-text">目标: < 5%</div>
                    </div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="turnoverRateChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">较目标低 1%，表现良好</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">65</div>
                        <div class="metric-label">员工"留任意愿"得分</div>
                        <div class="target-text">目标: 提升15% (69分)</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">↑ 8.3%</div>
                        <div class="text-xs text-gray-500">vs 上次</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="retentionScoreChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
    const grayColors = {
        primary: '#374151',
        secondary: '#6b7280',
        light: '#d1d5db',
        lighter: '#f3f4f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        }
    };

    // Training Participation Doughnut
    new Chart(document.getElementById('trainingParticipationChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [96, 4],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Training Satisfaction Doughnut
    new Chart(document.getElementById('trainingSatisfactionChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [82, 18],
                backgroundColor: [grayColors.warning, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Performance Distribution Pie Chart
    new Chart(document.getElementById('performanceDistributionChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: ['A/B+ (25%)', 'B (65%)', 'C/D (10%)'],
            datasets: [{
                data: [25, 65, 10],
                backgroundColor: [grayColors.primary, grayColors.secondary, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            plugins: { ...chartDefaults.plugins, legend: { display: true, position: 'bottom' } }
        }
    });

    // Improvement Plan Doughnut
    new Chart(document.getElementById('improvementPlanChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [85, 15],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Turnover Rate Bar Chart
    new Chart(document.getElementById('turnoverRateChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前', '目标上限'],
            datasets: [{
                data: [4, 5],
                backgroundColor: [grayColors.success, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 6, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Retention Score Bar Chart
    new Chart(document.getElementById('retentionScoreChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['上次', '当前', '目标'],
            datasets: [{
                data: [60, 65, 69],
                backgroundColor: [grayColors.light, grayColors.secondary, grayColors.primary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 80, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });
    </script>
</body>
</html>