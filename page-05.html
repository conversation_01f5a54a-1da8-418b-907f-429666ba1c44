<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5. 规避人才相关风险 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .metric-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        .metric-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .section-header {
            border-left: 4px solid #374151;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
        }
        .chart-container {
            height: 140px;
            position: relative;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }
        .target-text {
            font-size: 0.75rem;
            color: #9ca3af;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-good { background: #dcfce7; color: #166534; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-danger { background: #fecaca; color: #991b1b; }
        .zero-incidents {
            background: #1f2937;
            color: white;
            font-size: 3rem;
            font-weight: 800;
            border-radius: 50%;
            width: 5rem;
            height: 5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">规避人才相关风险</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 核心人才流失风险 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">核心人才流失风险</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">100%</div>
                        <div class="metric-label">核心人才库覆盖率</div>
                        <div class="target-text">目标: 100%</div>
                    </div>
                    <div class="status-badge status-good">✓ 完美</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="talentPoolCoverageChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 100%"></div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">1.5</div>
                        <div class="metric-label">核心岗位继任者准备度</div>
                        <div class="target-text">目标: 1-2名/岗位</div>
                    </div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="successionReadinessChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">平均每个核心岗位有 1.5 名继任者</div>
            </div>
        </div>

        <!-- 合规风险 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">合规风险</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">100%</div>
                        <div class="metric-label">劳动合同签订/续签率</div>
                        <div class="target-text">目标: 100%</div>
                    </div>
                    <div class="status-badge status-good">✓ 完美</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="contractComplianceChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 100%"></div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="zero-incidents">0</div>
                        <div>
                            <div class="metric-label">年度劳动纠纷案件数量</div>
                            <div class="target-text">目标: 0</div>
                        </div>
                    </div>
                    <div class="status-badge status-good">✓ 完美</div>
                </div>
                <div class="text-center mt-4">
                    <div class="text-lg font-semibold text-gray-900">零风险运营</div>
                    <div class="text-sm text-gray-500">全年无劳动纠纷案件</div>
                </div>
            </div>
        </div>

        <!-- 舆情风险 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">舆情风险</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">18h</div>
                        <div class="metric-label">负面舆情响应时间</div>
                        <div class="target-text">目标: < 24小时</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="prResponseTimeChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">快速响应，提前 6 小时达标</div>
            </div>

            <div class="metric-card p-6">
                <div class="metric-label mb-2">媒体报道倾向</div>
                <div class="target-text mb-4">目标: 正面/中性 ≥ 95%</div>
                <div class="chart-container mb-4">
                    <canvas id="mediaReportChart"></canvas>
                </div>
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-500">正面+中性: 96%</div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
            </div>
        </div>
    </div>

    <script>
    const grayColors = {
        primary: '#374151',
        secondary: '#6b7280',
        light: '#d1d5db',
        lighter: '#f3f4f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        }
    };

    // Talent Pool Coverage Doughnut
    new Chart(document.getElementById('talentPoolCoverageChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [100, 0],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Succession Readiness Bar Chart
    new Chart(document.getElementById('successionReadinessChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前平均', '目标低值', '目标高值'],
            datasets: [{
                data: [1.5, 1, 2],
                backgroundColor: [grayColors.primary, grayColors.light, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 3, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Contract Compliance Doughnut
    new Chart(document.getElementById('contractComplianceChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [100, 0],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // PR Response Time Bar Chart
    new Chart(document.getElementById('prResponseTimeChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前平均', '目标上限'],
            datasets: [{
                data: [18, 24],
                backgroundColor: [grayColors.success, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 30, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Media Report Pie Chart
    new Chart(document.getElementById('mediaReportChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: ['中性 (60%)', '正面 (36%)', '负面 (4%)'],
            datasets: [{
                data: [60, 36, 4],
                backgroundColor: [grayColors.secondary, grayColors.primary, grayColors.danger]
            }]
        },
        options: {
            ...chartDefaults,
            plugins: { ...chartDefaults.plugins, legend: { display: true, position: 'bottom' } }
        }
    });
    </script>
</body>
</html>