<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1. 支撑业务战略落地 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .main-container {
            height: calc(100vh - 60px);
            display: flex;
            gap: 1rem;
        }
        .left-panel {
            width: 35%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .right-panel {
            width: 65%;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            overflow-y: auto;
        }
        .metrics-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .insights-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            flex: 1;
        }
        .status-normal {
            color: #374151;
            font-weight: 600;
        }
        .status-warning {
            color: #6b7280;
            font-weight: 600;
        }
        .status-danger {
            color: #1f2937;
            font-weight: 600;
        }
        .tooltip {
            position: relative;
            cursor: help;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .tooltip-text {
            visibility: hidden;
            opacity: 0;
            width: 200px;
            background-color: #374151;
            color: white;
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            font-size: 12px;
            line-height: 1.4;
            transition: opacity 0.3s;
        }
        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #374151 transparent transparent transparent;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .chart-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }
        .chart-container {
            height: 200px;
            position: relative;
        }
        .chart-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-left: 4px solid #374151;
            padding-left: 0.75rem;
        }
        .quick-nav {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 0.5rem;
            z-index: 1000;
        }
        .nav-item {
            display: block;
            width: 40px;
            height: 40px;
            margin: 0.25rem 0;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        .nav-item:hover {
            background: #374151;
            color: white;
            transform: scale(1.1);
        }
        .nav-item.active {
            background: #374151;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">支撑业务战略落地</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="main-container">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Metrics Table -->
                <div class="metrics-table">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900">支撑业务战略落地 - 指标监控</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-xs">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">指标名称</th>
                                    <th class="px-2 py-2 text-center font-medium text-gray-700">状态</th>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">异常说明</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">战略岗位满足率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">新业务人力储备指数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">新业务线人力配备不足</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">编制执行偏差度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">数字化人才占比</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">占比偏低，影响数字化转型</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">销售队伍健康度</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">区域渗透匹配率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">三四线城市配置不匹配</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">成本效能警戒值</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">资质合规覆盖率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3">整体洞见分析</h4>
                    <div class="text-xs text-gray-700 space-y-2">
                        <div class="p-2 bg-gray-100 border-l-4 border-gray-800 rounded">
                            <p class="font-medium text-gray-900">关键风险</p>
                            <p class="text-gray-700">数字化人才占比严重不足，可能影响公司数字化转型战略的执行进度。</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-600 rounded">
                            <p class="font-medium text-gray-800">改进建议</p>
                            <p class="text-gray-700">1. 加快新业务线人才招聘，重点关注养老社区、互联网保险等新赛道<br>
                            2. 优化三四线城市人力配置，提升区域渗透匹配率</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-400 rounded">
                            <p class="font-medium text-gray-700">积极表现</p>
                            <p class="text-gray-600">战略岗位满足率、销售队伍健康度等核心指标表现良好，为业务战略落地提供了有力支撑。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Charts -->
            <div class="right-panel">
                <div class="section-title">数据可视化分析</div>

                <div class="chart-grid">
                    <div class="chart-item">
                        <div class="chart-title">战略岗位满足率趋势</div>
                        <div class="chart-container">
                            <canvas id="strategicPositionChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">新业务人力储备指数</div>
                        <div class="chart-container">
                            <canvas id="newBusinessChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">数字化人才占比分析</div>
                        <div class="chart-container">
                            <canvas id="digitalTalentChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">销售队伍健康度构成</div>
                        <div class="chart-container">
                            <canvas id="salesHealthChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-item">
                    <div class="chart-title">区域人力配置与市场潜力对比</div>
                    <div class="chart-container">
                        <canvas id="regionalChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    const colors = {
        primary: '#1f2937',
        secondary: '#374151',
        tertiary: '#6b7280',
        light: '#9ca3af',
        lighter: '#d1d5db',
        lightest: '#f3f4f6'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    font: { size: 10 }
                }
            },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: { color: colors.lightest },
                ticks: { font: { size: 10 } }
            },
            x: {
                grid: { display: false },
                ticks: { font: { size: 10 } }
            }
        }
    };

    // 战略岗位满足率趋势
    new Chart(document.getElementById('strategicPositionChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '战略岗位满足率',
                data: [92, 94, 96, 95, 97, 96],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                fill: true,
                tension: 0.4
            }, {
                label: '目标线',
                data: [95, 95, 95, 95, 95, 95],
                borderColor: colors.secondary,
                borderDash: [5, 5],
                fill: false
            }]
        },
        options: chartDefaults
    });

    // 新业务人力储备指数
    new Chart(document.getElementById('newBusinessChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['养老社区', '互联网保险', '健康险', '数字化平台'],
            datasets: [{
                label: '人力储备指数',
                data: [78, 82, 88, 75],
                backgroundColor: [colors.light, colors.tertiary, colors.secondary, colors.primary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                ...chartDefaults.scales,
                y: { ...chartDefaults.scales.y, max: 100 }
            }
        }
    });

    // 数字化人才占比分析
    new Chart(document.getElementById('digitalTalentChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['数字化人才', '传统岗位'],
            datasets: [{
                data: [15, 85],
                backgroundColor: [colors.primary, colors.lightest],
                borderWidth: 0
            }]
        },
        options: {
            ...chartDefaults,
            plugins: {
                ...chartDefaults.plugins,
                legend: { display: true, position: 'bottom' }
            }
        }
    });

    // 销售队伍健康度构成
    new Chart(document.getElementById('salesHealthChart').getContext('2d'), {
        type: 'radar',
        data: {
            labels: ['绩优人力占比', '主管占比', '13月留存率', '新人培养率', '团队稳定性'],
            datasets: [{
                label: '当前水平',
                data: [85, 78, 82, 75, 88],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '30',
                pointBackgroundColor: colors.primary
            }, {
                label: '目标水平',
                data: [90, 85, 85, 80, 90],
                borderColor: colors.secondary,
                backgroundColor: colors.secondary + '20',
                pointBackgroundColor: colors.secondary
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: { font: { size: 8 } }
                }
            }
        }
    });

    // 区域人力配置与市场潜力对比
    new Chart(document.getElementById('regionalChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['一线城市', '二线城市', '三线城市', '四线城市'],
            datasets: [{
                label: '人力配置指数',
                data: [95, 88, 72, 65],
                backgroundColor: colors.secondary
            }, {
                label: '市场潜力指数',
                data: [85, 92, 88, 82],
                backgroundColor: colors.tertiary
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                ...chartDefaults.scales,
                y: { ...chartDefaults.scales.y, max: 100 }
            }
        }
    });
    </script>
</body>
</html>