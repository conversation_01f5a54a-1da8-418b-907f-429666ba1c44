<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5. 规避人才相关风险 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .main-container {
            height: calc(100vh - 60px);
            display: flex;
            gap: 1rem;
        }
        .left-panel {
            width: 35%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .right-panel {
            width: 65%;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            overflow-y: auto;
        }
        .metrics-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .insights-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            flex: 1;
        }
        .status-normal {
            color: #374151;
            font-weight: 600;
        }
        .status-warning {
            color: #6b7280;
            font-weight: 600;
        }
        .status-danger {
            color: #1f2937;
            font-weight: 600;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .chart-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }
        .chart-container {
            height: 200px;
            position: relative;
        }
        .chart-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-left: 4px solid #374151;
            padding-left: 0.75rem;
        }
        .metric-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        .metric-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .section-header {
            border-left: 4px solid #374151;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
        }
        .chart-container {
            height: 140px;
            position: relative;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }
        .target-text {
            font-size: 0.75rem;
            color: #9ca3af;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">规避人才相关风险</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="main-container">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Metrics Table -->
                <div class="metrics-table">
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900">规避人才相关风险 - 指标监控</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-xs">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">指标名称</th>
                                    <th class="px-2 py-2 text-center font-medium text-gray-700">状态</th>
                                    <th class="px-2 py-2 text-left font-medium text-gray-700">异常说明</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">关键岗位空缺风险</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">部分关键岗位缺乏备选人员</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">合规培训覆盖率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">员工违规事件数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-danger">异常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">违规事件数量超出预警线</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">知识产权保护指数</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">数据安全事件率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">数据泄露风险需要关注</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">劳动争议发生率</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">业务连续性风险</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-warning">预警</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-600">关键业务依赖度过高</td>
                                </tr>
                                <tr>
                                    <td class="px-2 py-2 text-gray-900">监管合规评分</td>
                                    <td class="px-2 py-2 text-center">
                                        <span class="status-normal">正常</span>
                                    </td>
                                    <td class="px-2 py-2 text-gray-500">-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <h4 class="text-sm font-semibold text-gray-900 mb-3">整体洞见分析</h4>
                    <div class="text-xs text-gray-700 space-y-2">
                        <div class="p-2 bg-gray-100 border-l-4 border-gray-800 rounded">
                            <p class="font-medium text-gray-900">关键风险</p>
                            <p class="text-gray-700">员工违规事件数量异常，需要加强合规管理和风险防控。</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-600 rounded">
                            <p class="font-medium text-gray-800">改进建议</p>
                            <p class="text-gray-700">1. 加强关键岗位备选人员培养<br>
                            2. 提升数据安全防护能力<br>
                            3. 降低业务连续性风险，分散关键依赖</p>
                        </div>
                        <div class="p-2 bg-gray-50 border-l-4 border-gray-400 rounded">
                            <p class="font-medium text-gray-700">积极表现</p>
                            <p class="text-gray-600">合规培训覆盖率、监管合规评分等指标表现良好，风险管控体系基本健全。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Charts -->
            <div class="right-panel">
                <div class="section-title">数据可视化分析</div>

                <div class="chart-grid">
                    <div class="chart-item">
                        <div class="chart-title">关键岗位空缺风险分析</div>
                        <div class="chart-container">
                            <canvas id="keyPositionRiskChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">员工违规事件趋势</div>
                        <div class="chart-container">
                            <canvas id="complianceViolationChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">数据安全事件分析</div>
                        <div class="chart-container">
                            <canvas id="dataSecurityChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-item">
                        <div class="chart-title">业务连续性风险评估</div>
                        <div class="chart-container">
                            <canvas id="businessContinuityChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-item">
                    <div class="chart-title">监管合规评分对比</div>
                    <div class="chart-container">
                        <canvas id="regulatoryComplianceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
    const colors = {
        primary: '#1f2937',
        secondary: '#374151',
        tertiary: '#6b7280',
        light: '#9ca3af',
        lighter: '#d1d5db',
        lightest: '#f3f4f6'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    font: { size: 10 }
                }
            },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: { color: colors.lightest },
                ticks: { font: { size: 10 } }
            },
            x: {
                grid: { display: false },
                ticks: { font: { size: 10 } }
            }
        }
    };

    // 关键岗位空缺风险分析
    new Chart(document.getElementById('keyPositionRiskChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['CEO', 'CFO', 'CTO', '风控总监', '合规总监'],
            datasets: [{
                label: '备选人员数量',
                data: [2, 1, 0, 1, 2],
                backgroundColor: [colors.tertiary, colors.light, colors.primary, colors.light, colors.tertiary]
            }]
        },
        options: chartDefaults
    });

    // 员工违规事件趋势
    new Chart(document.getElementById('complianceViolationChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '违规事件数',
                data: [2, 1, 4, 3, 6, 5],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                fill: true,
                tension: 0.4
            }, {
                label: '预警线',
                data: [3, 3, 3, 3, 3, 3],
                borderColor: colors.secondary,
                borderDash: [5, 5],
                fill: false
            }]
        },
        options: chartDefaults
    });

    // 数据安全事件分析
    new Chart(document.getElementById('dataSecurityChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['轻微事件', '一般事件', '严重事件'],
            datasets: [{
                data: [8, 3, 1],
                backgroundColor: [colors.light, colors.tertiary, colors.primary],
                borderWidth: 0
            }]
        },
        options: {
            ...chartDefaults,
            plugins: {
                ...chartDefaults.plugins,
                legend: { display: true, position: 'bottom' }
            }
        }
    });

    // 业务连续性风险评估
    new Chart(document.getElementById('businessContinuityChart').getContext('2d'), {
        type: 'radar',
        data: {
            labels: ['人员依赖', '系统依赖', '供应商依赖', '流程依赖', '知识依赖'],
            datasets: [{
                label: '风险等级',
                data: [75, 60, 45, 55, 80],
                borderColor: colors.primary,
                backgroundColor: colors.primary + '30',
                pointBackgroundColor: colors.primary
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: { font: { size: 8 } }
                }
            }
        }
    });

    // 监管合规评分对比
    new Chart(document.getElementById('regulatoryComplianceChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['销售合规', '投资合规', '理赔合规', '数据合规', '反洗钱'],
            datasets: [{
                label: '当前评分',
                data: [85, 92, 88, 78, 95],
                backgroundColor: colors.secondary
            }, {
                label: '监管要求',
                data: [80, 85, 85, 80, 90],
                backgroundColor: colors.tertiary
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                ...chartDefaults.scales,
                y: { ...chartDefaults.scales.y, max: 100 }
            }
        }
    });
    </script>
</body>
</html>


            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">100%</div>
                        <div class="metric-label">核心人才库覆盖率</div>
                        <div class="target-text">目标: 100%</div>
                    </div>
                    <div class="status-badge status-good">✓ 完美</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="talentPoolCoverageChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 100%"></div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">1.5</div>
                        <div class="metric-label">核心岗位继任者准备度</div>
                        <div class="target-text">目标: 1-2名/岗位</div>
                    </div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="successionReadinessChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">平均每个核心岗位有 1.5 名继任者</div>
            </div>
        </div>

        <!-- 合规风险 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">合规风险</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">100%</div>
                        <div class="metric-label">劳动合同签订/续签率</div>
                        <div class="target-text">目标: 100%</div>
                    </div>
                    <div class="status-badge status-good">✓ 完美</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="contractComplianceChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 100%"></div>
                </div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="zero-incidents">0</div>
                        <div>
                            <div class="metric-label">年度劳动纠纷案件数量</div>
                            <div class="target-text">目标: 0</div>
                        </div>
                    </div>
                    <div class="status-badge status-good">✓ 完美</div>
                </div>
                <div class="text-center mt-4">
                    <div class="text-lg font-semibold text-gray-900">零风险运营</div>
                    <div class="text-sm text-gray-500">全年无劳动纠纷案件</div>
                </div>
            </div>
        </div>

        <!-- 舆情风险 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">舆情风险</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">18h</div>
                        <div class="metric-label">负面舆情响应时间</div>
                        <div class="target-text">目标: < 24小时</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="prResponseTimeChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">快速响应，提前 6 小时达标</div>
            </div>

            <div class="metric-card p-6">
                <div class="metric-label mb-2">媒体报道倾向</div>
                <div class="target-text mb-4">目标: 正面/中性 ≥ 95%</div>
                <div class="chart-container mb-4">
                    <canvas id="mediaReportChart"></canvas>
                </div>
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-500">正面+中性: 96%</div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
            </div>
        </div>
    </div>

    <script>
    const grayColors = {
        primary: '#374151',
        secondary: '#6b7280',
        light: '#d1d5db',
        lighter: '#f3f4f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        }
    };

    // Talent Pool Coverage Doughnut
    new Chart(document.getElementById('talentPoolCoverageChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [100, 0],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Succession Readiness Bar Chart
    new Chart(document.getElementById('successionReadinessChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前平均', '目标低值', '目标高值'],
            datasets: [{
                data: [1.5, 1, 2],
                backgroundColor: [grayColors.primary, grayColors.light, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 3, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Contract Compliance Doughnut
    new Chart(document.getElementById('contractComplianceChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [100, 0],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // PR Response Time Bar Chart
    new Chart(document.getElementById('prResponseTimeChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前平均', '目标上限'],
            datasets: [{
                data: [18, 24],
                backgroundColor: [grayColors.success, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 30, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Media Report Pie Chart
    new Chart(document.getElementById('mediaReportChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: ['中性 (60%)', '正面 (36%)', '负面 (4%)'],
            datasets: [{
                data: [60, 36, 4],
                backgroundColor: [grayColors.secondary, grayColors.primary, grayColors.danger]
            }]
        },
        options: {
            ...chartDefaults,
            plugins: { ...chartDefaults.plugins, legend: { display: true, position: 'bottom' } }
        }
    });
    </script>
</body>
</html>