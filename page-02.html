<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2. 保障人才持续输入 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            height: 100vh;
            overflow: hidden;
        }
        .main-container {
            height: calc(100vh - 60px);
            display: flex;
            gap: 1rem;
        }
        .left-panel {
            width: 35%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .right-panel {
            width: 65%;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            overflow-y: auto;
        }
        .metrics-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .insights-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            flex: 1;
        }
        .status-normal {
            color: #10b981;
            font-weight: 600;
        }
        .status-warning {
            color: #f59e0b;
            font-weight: 600;
        }
        .status-danger {
            color: #ef4444;
            font-weight: 600;
        }
        .tooltip {
            position: relative;
            cursor: help;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .tooltip-text {
            visibility: hidden;
            opacity: 0;
            width: 200px;
            background-color: #374151;
            color: white;
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            font-size: 12px;
            line-height: 1.4;
            transition: opacity 0.3s;
        }
        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #374151 transparent transparent transparent;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .chart-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e2e8f0;
        }
        .chart-container {
            height: 200px;
            position: relative;
        }
        .chart-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-left: 4px solid #374151;
            padding-left: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">保障人才持续输入</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 招聘渠道管理 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">招聘渠道管理</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">75%</div>
                        <div class="metric-label">关键岗位优质渠道占比</div>
                        <div class="target-text">目标: 猎头+内推 ≥ 70%</div>
                    </div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="channelRatioChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">猎头 40% + 内推 35% = 75%</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">88%</div>
                        <div class="metric-label">校园招聘Offer接受率</div>
                        <div class="target-text">目标: 85%</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="offerAcceptanceRateChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 88%"></div>
                </div>
            </div>
        </div>

        <!-- 招聘流程优化 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">招聘流程优化</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">35</div>
                        <div class="metric-label">平均招聘周期（天）</div>
                        <div class="target-text">目标: 30天</div>
                    </div>
                    <div class="status-badge status-warning">需改进</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="recruitmentCycleChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">较目标超出 5 天，需要优化流程效率</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">92%</div>
                        <div class="metric-label">候选人面试体验满意度</div>
                        <div class="target-text">目标: 90%</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="candidateExperienceChart"></canvas>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gray-800 h-2 rounded-full" style="width: 92%"></div>
                </div>
            </div>
        </div>

        <!-- 雇主品牌建设 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">雇主品牌建设</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="metric-label mb-2">主流招聘平台排名</div>
                <div class="target-text mb-4">目标: 前三</div>
                <div class="chart-container mb-4">
                    <canvas id="platformRankingChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">平台A: 第2名 ✓ | 平台B: 第4名 | 平台C: 第3名 ✓</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">150%</div>
                        <div class="metric-label">公众号粉丝年度增长</div>
                        <div class="target-text">目标: 50%</div>
                    </div>
                    <div class="status-badge status-good">✓ 超额</div>
                </div>
                <div class="chart-container">
                    <canvas id="followerGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
    const grayColors = {
        primary: '#374151',
        secondary: '#6b7280',
        light: '#d1d5db',
        lighter: '#f3f4f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        }
    };

    // Channel Ratio Pie Chart
    new Chart(document.getElementById('channelRatioChart').getContext('2d'), {
        type: 'pie',
        data: {
            labels: ['猎头', '内推', '其他'],
            datasets: [{
                data: [40, 35, 25],
                backgroundColor: [grayColors.primary, grayColors.secondary, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            plugins: { ...chartDefaults.plugins, legend: { display: true, position: 'bottom' } }
        }
    });

    // Offer Acceptance Rate Doughnut
    new Chart(document.getElementById('offerAcceptanceRateChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [88, 12],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Recruitment Cycle Bar Chart
    new Chart(document.getElementById('recruitmentCycleChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前', '目标'],
            datasets: [{
                data: [35, 30],
                backgroundColor: [grayColors.warning, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Candidate Experience Doughnut
    new Chart(document.getElementById('candidateExperienceChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [92, 8],
                backgroundColor: [grayColors.primary, grayColors.lighter],
                cutout: '75%'
            }]
        },
        options: { ...chartDefaults, cutout: '75%' }
    });

    // Platform Ranking Bar Chart
    new Chart(document.getElementById('platformRankingChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['平台A', '平台B', '平台C'],
            datasets: [{
                label: '排名 (越低越好)',
                data: [2, 4, 3],
                backgroundColor: [grayColors.success, grayColors.danger, grayColors.success]
            }]
        },
        options: {
            ...chartDefaults,
            indexAxis: 'y',
            scales: {
                x: { beginAtZero: true, max: 5, grid: { color: grayColors.lighter } },
                y: { grid: { display: false } }
            }
        }
    });

    // Follower Growth Line Chart
    new Chart(document.getElementById('followerGrowthChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '粉丝数(K)',
                data: [10, 12, 15, 18, 22, 25],
                borderColor: grayColors.primary,
                backgroundColor: grayColors.primary + '20',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });
    </script>
</body>
</html>
