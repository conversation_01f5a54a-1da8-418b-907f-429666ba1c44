<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4. 驱动人力资本效能 - 人力资源数据仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .metric-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        .metric-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .section-header {
            border-left: 4px solid #374151;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
        }
        .chart-container {
            height: 140px;
            position: relative;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }
        .target-text {
            font-size: 0.75rem;
            color: #9ca3af;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-good { background: #dcfce7; color: #166534; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-danger { background: #fecaca; color: #991b1b; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <h1 class="text-xl font-semibold text-gray-900">驱动人力资本效能</h1>
            <a href="index.html" class="text-sm text-gray-600 hover:text-gray-900 transition-colors">← 返回总览</a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 人效分析 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">人效分析</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">16%</div>
                        <div class="metric-label">人均营收年度增长率</div>
                        <div class="target-text">目标: 15%</div>
                    </div>
                    <div class="status-badge status-good">✓ 超额</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="revenuePerEmployeeChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">超出目标 1%，人效提升显著</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">8%</div>
                        <div class="metric-label">劳动生产率提升</div>
                        <div class="target-text">目标: 10%</div>
                    </div>
                    <div class="status-badge status-warning">需改进</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="laborProductivityChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">距离目标差 2%，需要优化生产流程</div>
            </div>
        </div>

        <!-- 薪酬竞争力 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">薪酬竞争力</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">78th</div>
                        <div class="metric-label">核心岗位薪酬市场分位值</div>
                        <div class="target-text">目标: ≥ 75th</div>
                    </div>
                    <div class="status-badge status-good">✓ 优秀</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="salaryCompetitivenessChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">高于市场基准 3 个分位值</div>
            </div>

            <div class="metric-card p-6">
                <div class="metric-label mb-2">薪酬增长 vs 利润增长控制</div>
                <div class="target-text mb-4">目标: 薪酬增长 ≤ 利润增长*80%</div>
                <div class="chart-container mb-4">
                    <canvas id="salaryProfitGrowthChart"></canvas>
                </div>
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-500">薪酬增长 9% ≤ 控制线 12%</div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
            </div>
        </div>

        <!-- 组织结构优化 -->
        <div class="section-header">
            <h2 class="text-lg font-semibold text-gray-900">组织结构优化</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">5</div>
                        <div class="metric-label">管理层级</div>
                        <div class="target-text">目标: ≤ 5级</div>
                    </div>
                    <div class="status-badge status-good">✓ 达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="managementLevelChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">已达到扁平化管理目标</div>
            </div>

            <div class="metric-card p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="metric-value">7.5</div>
                        <div class="metric-label">平均管理幅度</div>
                        <div class="target-text">目标: 1:8</div>
                    </div>
                    <div class="status-badge status-warning">接近达标</div>
                </div>
                <div class="chart-container mb-4">
                    <canvas id="spanOfControlChart"></canvas>
                </div>
                <div class="text-xs text-gray-500">距离目标差 0.5，管理效率待提升</div>
            </div>
        </div>
    </div>

    <script>
    const grayColors = {
        primary: '#374151',
        secondary: '#6b7280',
        light: '#d1d5db',
        lighter: '#f3f4f6',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444'
    };

    const chartDefaults = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: true }
        },
        elements: {
            bar: { borderRadius: 4 },
            arc: { borderWidth: 0 }
        }
    };

    // Revenue per Employee Growth
    new Chart(document.getElementById('revenuePerEmployeeChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前', '目标'],
            datasets: [{
                data: [16, 15],
                backgroundColor: [grayColors.success, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 20, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Labor Productivity Growth
    new Chart(document.getElementById('laborProductivityChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前', '目标'],
            datasets: [{
                data: [8, 10],
                backgroundColor: [grayColors.warning, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 12, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Salary Competitiveness
    new Chart(document.getElementById('salaryCompetitivenessChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前分位值', '目标分位值'],
            datasets: [{
                data: [78, 75],
                backgroundColor: [grayColors.success, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 100, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Salary vs Profit Growth
    new Chart(document.getElementById('salaryProfitGrowthChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['薪酬增长率', '控制线(利润*80%)'],
            datasets: [{
                data: [9, 12],
                backgroundColor: [grayColors.primary, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 15, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Management Levels
    new Chart(document.getElementById('managementLevelChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前层级', '目标上限'],
            datasets: [{
                data: [5, 5],
                backgroundColor: [grayColors.primary, grayColors.light]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 7, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });

    // Span of Control
    new Chart(document.getElementById('spanOfControlChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['当前幅度', '目标幅度'],
            datasets: [{
                data: [7.5, 8],
                backgroundColor: [grayColors.warning, grayColors.secondary]
            }]
        },
        options: {
            ...chartDefaults,
            scales: {
                y: { beginAtZero: true, max: 10, grid: { color: grayColors.lighter } },
                x: { grid: { display: false } }
            }
        }
    });
    </script>
</body>
</html>